/**
 * @file websocket_led_parser.c
 * @brief WebSocket LED控制协议解析库 - 实现文件
 * @version 1.0
 * @date 2024-12-17
 */

#include "websocket_led_parser.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <ctype.h>

/* ========== 内部函数声明 ========== */
static ws_led_error_t parse_led_command(const char* cmd_str, ws_led_command_t* cmd);
static ws_led_error_t execute_command(ws_led_parser_t* parser, const ws_led_command_t* cmd);
static void trim_string(char* str);
static bool is_valid_command_format(const char* cmd_str);

/* ========== 错误消息表 ========== */
static const char* error_messages[] = {
    "Success",                           // WS_LED_OK
    "Null pointer error",                // WS_LED_ERROR_NULL_POINTER
    "Invalid buffer",                    // WS_LED_ERROR_INVALID_BUFFER
    "Buffer overflow",                   // WS_LED_ERROR_BUFFER_OVERFLOW
    "Invalid command",                   // WS_LED_ERROR_INVALID_COMMAND
    "Invalid LED index",                 // WS_LED_ERROR_INVALID_LED_INDEX
    "Parse failed",                      // WS_LED_ERROR_PARSE_FAILED
    "Callback not set",                  // WS_LED_ERROR_CALLBACK_NOT_SET
    "Timeout error"                      // WS_LED_ERROR_TIMEOUT
};

/* ========== API函数实现 ========== */

/**
 * @brief 初始化WebSocket LED解析器
 */
ws_led_error_t ws_led_parser_init(ws_led_parser_t* parser, const ws_led_parser_config_t* config) {
    if (!parser || !config) {
        return WS_LED_ERROR_NULL_POINTER;
    }
    
    // 检查必要的回调函数
    if (!config->led_control_cb) {
        return WS_LED_ERROR_CALLBACK_NOT_SET;
    }
    
    // 初始化解析器
    memset(parser, 0, sizeof(ws_led_parser_t));
    memcpy(&parser->config, config, sizeof(ws_led_parser_config_t));
    
    // 设置默认值
    if (parser->config.command_timeout_ms == 0) {
        parser->config.command_timeout_ms = WS_LED_TIMEOUT_MS;
    }
    
    parser->initialized = true;
    
    return WS_LED_OK;
}

/**
 * @brief 解析WebSocket接收到的数据
 */
ws_led_error_t ws_led_parser_parse(ws_led_parser_t* parser, const char* buffer, uint16_t length) {
    if (!parser || !buffer) {
        return WS_LED_ERROR_NULL_POINTER;
    }
    
    if (!parser->initialized) {
        return WS_LED_ERROR_INVALID_BUFFER;
    }
    
    if (length == 0 || length > WS_LED_MAX_BUFFER_SIZE) {
        return WS_LED_ERROR_INVALID_BUFFER;
    }
    
    // 创建临时缓冲区并复制数据
    char temp_buffer[WS_LED_MAX_BUFFER_SIZE + 1];
    memcpy(temp_buffer, buffer, length);
    temp_buffer[length] = '\0';
    
    // 去除首尾空白字符
    trim_string(temp_buffer);
    
    // 检查是否为空命令
    if (strlen(temp_buffer) == 0) {
        return WS_LED_OK;
    }
    
    // 解析命令
    ws_led_command_t cmd;
    ws_led_error_t result = ws_led_parse_command(parser, temp_buffer, &cmd);
    
    if (result == WS_LED_OK) {
        // 执行命令
        result = execute_command(parser, &cmd);
    } else if (parser->config.error_cb) {
        // 调用错误回调
        parser->config.error_cb(result, ws_led_get_error_string(result));
    }
    
    return result;
}

/**
 * @brief 解析单个命令
 */
ws_led_error_t ws_led_parse_command(ws_led_parser_t* parser, const char* command_str, ws_led_command_t* cmd) {
    if (!parser || !command_str || !cmd) {
        return WS_LED_ERROR_NULL_POINTER;
    }
    
    // 初始化命令结构体
    memset(cmd, 0, sizeof(ws_led_command_t));
    cmd->type = WS_LED_CMD_UNKNOWN;
    
    // 检查命令格式
    if (!is_valid_command_format(command_str)) {
        return WS_LED_ERROR_INVALID_COMMAND;
    }
    
    // 转换为大写进行比较
    char upper_cmd[WS_LED_MAX_COMMAND_LEN];
    strncpy(upper_cmd, command_str, sizeof(upper_cmd) - 1);
    upper_cmd[sizeof(upper_cmd) - 1] = '\0';
    
    for (int i = 0; upper_cmd[i]; i++) {
        upper_cmd[i] = toupper(upper_cmd[i]);
    }
    
    // 解析心跳包
    if (strcmp(upper_cmd, "PING") == 0) {
        cmd->type = WS_LED_CMD_PING;
        return WS_LED_OK;
    } else if (strcmp(upper_cmd, "PONG") == 0) {
        cmd->type = WS_LED_CMD_PONG;
        return WS_LED_OK;
    }
    
    // 解析LED命令
    return parse_led_command(upper_cmd, cmd);
}

/**
 * @brief 生成LED状态响应字符串
 */
ws_led_error_t ws_led_generate_response(uint8_t led_index, ws_led_state_t state, char* response, uint16_t max_len) {
    if (!response || max_len < 10) {  // "LED8:OFF\0" = 9字符
        return WS_LED_ERROR_NULL_POINTER;
    }
    
    if (!ws_led_is_valid_index(led_index)) {
        return WS_LED_ERROR_INVALID_LED_INDEX;
    }
    
    snprintf(response, max_len, "LED%d:%s", led_index + 1, ws_led_state_to_string(state));
    
    return WS_LED_OK;
}

/**
 * @brief 生成心跳包响应
 */
ws_led_error_t ws_led_generate_heartbeat(bool is_pong, char* response, uint16_t max_len) {
    if (!response || max_len < 5) {  // "pong\0" = 5字符
        return WS_LED_ERROR_NULL_POINTER;
    }
    
    strncpy(response, is_pong ? "pong" : "ping", max_len - 1);
    response[max_len - 1] = '\0';
    
    return WS_LED_OK;
}

/**
 * @brief 获取错误码对应的错误消息
 */
const char* ws_led_get_error_string(ws_led_error_t error_code) {
    if (error_code >= 0 && error_code < sizeof(error_messages) / sizeof(error_messages[0])) {
        return error_messages[error_code];
    }
    return "Unknown error";
}

/**
 * @brief 重置解析器状态
 */
ws_led_error_t ws_led_parser_reset(ws_led_parser_t* parser) {
    if (!parser) {
        return WS_LED_ERROR_NULL_POINTER;
    }
    
    parser->buffer_pos = 0;
    parser->last_command_time = 0;
    memset(parser->command_buffer, 0, sizeof(parser->command_buffer));
    
    return WS_LED_OK;
}

/**
 * @brief 设置当前时间戳
 */
ws_led_error_t ws_led_set_timestamp(ws_led_parser_t* parser, uint32_t timestamp) {
    if (!parser) {
        return WS_LED_ERROR_NULL_POINTER;
    }
    
    parser->last_command_time = timestamp;
    
    return WS_LED_OK;
}

/* ========== 工具函数实现 ========== */

/**
 * @brief 检查LED索引是否有效
 */
bool ws_led_is_valid_index(uint8_t led_index) {
    return led_index < WS_LED_MAX_LED_COUNT;
}

/**
 * @brief 字符串转换为LED状态
 */
ws_led_state_t ws_led_string_to_state(const char* state_str) {
    if (!state_str) {
        return WS_LED_STATE_OFF;
    }
    
    if (strcasecmp(state_str, "ON") == 0 || strcasecmp(state_str, "1") == 0) {
        return WS_LED_STATE_ON;
    }
    
    return WS_LED_STATE_OFF;
}

/**
 * @brief LED状态转换为字符串
 */
const char* ws_led_state_to_string(ws_led_state_t state) {
    return (state == WS_LED_STATE_ON) ? "ON" : "OFF";
}

/* ========== 内部函数实现 ========== */

/**
 * @brief 解析LED命令
 */
static ws_led_error_t parse_led_command(const char* cmd_str, ws_led_command_t* cmd) {
    // 查找冒号分隔符
    char* colon_pos = strchr(cmd_str, ':');
    if (!colon_pos) {
        return WS_LED_ERROR_INVALID_COMMAND;
    }
    
    // 分离LED编号和状态
    char led_part[16];
    char state_part[16];
    
    size_t led_len = colon_pos - cmd_str;
    if (led_len >= sizeof(led_part)) {
        return WS_LED_ERROR_INVALID_COMMAND;
    }
    
    strncpy(led_part, cmd_str, led_len);
    led_part[led_len] = '\0';
    
    strncpy(state_part, colon_pos + 1, sizeof(state_part) - 1);
    state_part[sizeof(state_part) - 1] = '\0';
    
    // 解析LED编号
    if (strncmp(led_part, "LED", 3) != 0) {
        return WS_LED_ERROR_INVALID_COMMAND;
    }
    
    int led_num = atoi(led_part + 3);
    if (led_num < 1 || led_num > WS_LED_MAX_LED_COUNT) {
        return WS_LED_ERROR_INVALID_LED_INDEX;
    }
    
    cmd->led_index = led_num - 1;  // 转换为0-7索引
    cmd->state = ws_led_string_to_state(state_part);
    cmd->type = (cmd->state == WS_LED_STATE_ON) ? WS_LED_CMD_LED_ON : WS_LED_CMD_LED_OFF;
    
    return WS_LED_OK;
}

/**
 * @brief 执行命令
 */
static ws_led_error_t execute_command(ws_led_parser_t* parser, const ws_led_command_t* cmd) {
    bool success = false;
    
    switch (cmd->type) {
        case WS_LED_CMD_LED_ON:
        case WS_LED_CMD_LED_OFF:
            if (parser->config.led_control_cb) {
                success = parser->config.led_control_cb(cmd->led_index, cmd->state);
                
                // 自动响应
                if (success && parser->config.auto_response && parser->config.response_cb) {
                    char response[32];
                    if (ws_led_generate_response(cmd->led_index, cmd->state, response, sizeof(response)) == WS_LED_OK) {
                        parser->config.response_cb(response, strlen(response));
                    }
                }
            }
            break;
            
        case WS_LED_CMD_PING:
            if (parser->config.enable_heartbeat && parser->config.heartbeat_cb) {
                success = parser->config.heartbeat_cb(true);
                
                // 自动回复pong
                if (success && parser->config.auto_response && parser->config.response_cb) {
                    char response[8];
                    if (ws_led_generate_heartbeat(true, response, sizeof(response)) == WS_LED_OK) {
                        parser->config.response_cb(response, strlen(response));
                    }
                }
            }
            break;
            
        case WS_LED_CMD_PONG:
            if (parser->config.enable_heartbeat && parser->config.heartbeat_cb) {
                success = parser->config.heartbeat_cb(false);
            }
            break;
            
        default:
            return WS_LED_ERROR_INVALID_COMMAND;
    }
    
    return success ? WS_LED_OK : WS_LED_ERROR_PARSE_FAILED;
}

/**
 * @brief 去除字符串首尾空白字符
 */
static void trim_string(char* str) {
    if (!str) return;
    
    // 去除尾部空白
    int len = strlen(str);
    while (len > 0 && isspace(str[len - 1])) {
        str[--len] = '\0';
    }
    
    // 去除头部空白
    char* start = str;
    while (*start && isspace(*start)) {
        start++;
    }
    
    if (start != str) {
        memmove(str, start, strlen(start) + 1);
    }
}

/**
 * @brief 检查命令格式是否有效
 */
static bool is_valid_command_format(const char* cmd_str) {
    if (!cmd_str || strlen(cmd_str) == 0) {
        return false;
    }
    
    // 检查长度
    if (strlen(cmd_str) > WS_LED_MAX_COMMAND_LEN - 1) {
        return false;
    }
    
    // 基本格式检查
    return true;
}
