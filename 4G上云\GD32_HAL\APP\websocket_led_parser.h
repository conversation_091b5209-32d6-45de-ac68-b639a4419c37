/**
 * @file websocket_led_parser.h
 * @brief WebSocket LED控制协议解析库 - 头文件
 * @version 1.0
 * @date 2024-12-17
 * 
 * 功能特点：
 * - 低耦合设计，通过回调函数与具体硬件解耦
 * - 支持LED控制命令解析（LED1:ON/OFF格式）
 * - 支持心跳包处理（ping/pong）
 * - 简单易用的API接口
 * - 内存占用小，适合嵌入式系统
 */

#ifndef WEBSOCKET_LED_PARSER_H
#define WEBSOCKET_LED_PARSER_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

/* ========== 配置参数 ========== */
#define WS_LED_MAX_BUFFER_SIZE    128    // 最大缓冲区大小
#define WS_LED_MAX_COMMAND_LEN    32     // 最大命令长度
#define WS_LED_MAX_LED_COUNT      8      // 支持的LED数量
#define WS_LED_TIMEOUT_MS         5000   // 命令超时时间(ms)

/* ========== 错误码定义 ========== */
typedef enum {
    WS_LED_OK = 0,                       // 成功
    WS_LED_ERROR_NULL_POINTER,           // 空指针错误
    WS_LED_ERROR_INVALID_BUFFER,         // 无效缓冲区
    WS_LED_ERROR_BUFFER_OVERFLOW,        // 缓冲区溢出
    WS_LED_ERROR_INVALID_COMMAND,        // 无效命令
    WS_LED_ERROR_INVALID_LED_INDEX,      // 无效LED索引
    WS_LED_ERROR_PARSE_FAILED,           // 解析失败
    WS_LED_ERROR_CALLBACK_NOT_SET,       // 回调函数未设置
    WS_LED_ERROR_TIMEOUT                 // 超时错误
} ws_led_error_t;

/* ========== 命令类型定义 ========== */
typedef enum {
    WS_LED_CMD_UNKNOWN = 0,              // 未知命令
    WS_LED_CMD_LED_ON,                   // LED开启命令
    WS_LED_CMD_LED_OFF,                  // LED关闭命令
    WS_LED_CMD_PING,                     // 心跳包ping
    WS_LED_CMD_PONG,                     // 心跳包pong
    WS_LED_CMD_ALL_ON,                   // 全部LED开启
    WS_LED_CMD_ALL_OFF                   // 全部LED关闭
} ws_led_cmd_type_t;

/* ========== LED状态定义 ========== */
typedef enum {
    WS_LED_STATE_OFF = 0,                // LED关闭
    WS_LED_STATE_ON = 1                  // LED开启
} ws_led_state_t;

/* ========== 命令结构体 ========== */
typedef struct {
    ws_led_cmd_type_t type;              // 命令类型
    uint8_t led_index;                   // LED索引(0-7)
    ws_led_state_t state;                // LED状态
    uint32_t timestamp;                  // 时间戳(可选)
} ws_led_command_t;

/* ========== 回调函数类型定义 ========== */

/**
 * @brief LED控制回调函数类型
 * @param led_index LED索引(0-7)
 * @param state LED状态(0=关闭, 1=开启)
 * @return true=成功, false=失败
 */
typedef bool (*ws_led_control_callback_t)(uint8_t led_index, ws_led_state_t state);

/**
 * @brief 心跳包回调函数类型
 * @param is_ping true=收到ping, false=收到pong
 * @return true=成功, false=失败
 */
typedef bool (*ws_led_heartbeat_callback_t)(bool is_ping);

/**
 * @brief 状态反馈回调函数类型
 * @param response 要发送的响应字符串
 * @param length 响应字符串长度
 * @return true=成功, false=失败
 */
typedef bool (*ws_led_response_callback_t)(const char* response, uint16_t length);

/**
 * @brief 错误处理回调函数类型
 * @param error_code 错误码
 * @param error_msg 错误消息
 */
typedef void (*ws_led_error_callback_t)(ws_led_error_t error_code, const char* error_msg);

/* ========== 解析器配置结构体 ========== */
typedef struct {
    ws_led_control_callback_t led_control_cb;      // LED控制回调
    ws_led_heartbeat_callback_t heartbeat_cb;       // 心跳包回调
    ws_led_response_callback_t response_cb;         // 响应回调
    ws_led_error_callback_t error_cb;               // 错误回调
    bool auto_response;                             // 是否自动响应
    bool enable_heartbeat;                          // 是否启用心跳包
    uint32_t command_timeout_ms;                    // 命令超时时间
} ws_led_parser_config_t;

/* ========== 解析器句柄结构体 ========== */
typedef struct {
    ws_led_parser_config_t config;                 // 配置信息
    char command_buffer[WS_LED_MAX_COMMAND_LEN];    // 命令缓冲区
    uint16_t buffer_pos;                            // 缓冲区位置
    uint32_t last_command_time;                     // 上次命令时间
    bool initialized;                               // 初始化标志
} ws_led_parser_t;

/* ========== API函数声明 ========== */

/**
 * @brief 初始化WebSocket LED解析器
 * @param parser 解析器句柄指针
 * @param config 配置参数指针
 * @return ws_led_error_t 错误码
 */
ws_led_error_t ws_led_parser_init(ws_led_parser_t* parser, const ws_led_parser_config_t* config);

/**
 * @brief 解析WebSocket接收到的数据
 * @param parser 解析器句柄指针
 * @param buffer 接收数据缓冲区
 * @param length 数据长度
 * @return ws_led_error_t 错误码
 */
ws_led_error_t ws_led_parser_parse(ws_led_parser_t* parser, const char* buffer, uint16_t length);

/**
 * @brief 解析单个命令
 * @param parser 解析器句柄指针
 * @param command_str 命令字符串
 * @param cmd 解析结果输出
 * @return ws_led_error_t 错误码
 */
ws_led_error_t ws_led_parse_command(ws_led_parser_t* parser, const char* command_str, ws_led_command_t* cmd);

/**
 * @brief 生成LED状态响应字符串
 * @param led_index LED索引(0-7)
 * @param state LED状态
 * @param response 输出缓冲区
 * @param max_len 缓冲区最大长度
 * @return ws_led_error_t 错误码
 */
ws_led_error_t ws_led_generate_response(uint8_t led_index, ws_led_state_t state, char* response, uint16_t max_len);

/**
 * @brief 生成心跳包响应
 * @param is_pong true=生成pong, false=生成ping
 * @param response 输出缓冲区
 * @param max_len 缓冲区最大长度
 * @return ws_led_error_t 错误码
 */
ws_led_error_t ws_led_generate_heartbeat(bool is_pong, char* response, uint16_t max_len);

/**
 * @brief 获取错误码对应的错误消息
 * @param error_code 错误码
 * @return const char* 错误消息字符串
 */
const char* ws_led_get_error_string(ws_led_error_t error_code);

/**
 * @brief 重置解析器状态
 * @param parser 解析器句柄指针
 * @return ws_led_error_t 错误码
 */
ws_led_error_t ws_led_parser_reset(ws_led_parser_t* parser);

/**
 * @brief 设置当前时间戳(用于超时检测)
 * @param parser 解析器句柄指针
 * @param timestamp 当前时间戳(ms)
 * @return ws_led_error_t 错误码
 */
ws_led_error_t ws_led_set_timestamp(ws_led_parser_t* parser, uint32_t timestamp);

/* ========== 工具函数 ========== */

/**
 * @brief 检查LED索引是否有效
 * @param led_index LED索引
 * @return bool true=有效, false=无效
 */
bool ws_led_is_valid_index(uint8_t led_index);

/**
 * @brief 字符串转换为LED状态
 * @param state_str 状态字符串("ON"/"OFF")
 * @return ws_led_state_t LED状态
 */
ws_led_state_t ws_led_string_to_state(const char* state_str);

/**
 * @brief LED状态转换为字符串
 * @param state LED状态
 * @return const char* 状态字符串
 */
const char* ws_led_state_to_string(ws_led_state_t state);

#ifdef __cplusplus
}
#endif

#endif /* WEBSOCKET_LED_PARSER_H */
