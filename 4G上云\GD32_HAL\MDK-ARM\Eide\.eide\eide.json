{"name": "GD32_Xifeng", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "../startup_stm32f429xx.s"}], "folders": []}, {"name": "User", "files": [], "folders": [{"name": "Core", "files": [{"path": "../../Core/Src/main.c"}, {"path": "../../Core/Src/gpio.c"}, {"path": "../../Core/Src/adc.c"}, {"path": "../../Core/Src/dac.c"}, {"path": "../../Core/Src/dma.c"}, {"path": "../../Core/Src/tim.c"}, {"path": "../../Core/Src/usart.c"}, {"path": "../../Core/Src/stm32f4xx_it.c"}, {"path": "../../Core/Src/stm32f4xx_hal_msp.c"}], "folders": []}]}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32F4xx_HAL_Driver", "files": [{"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac_ex.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c"}, {"path": "../../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../../Core/Src/system_stm32f4xx.c"}], "folders": []}]}, {"name": "Components", "files": [], "folders": [{"name": "ebtn", "files": [{"path": "../../Components/ebtn/ebtn.c"}], "folders": []}, {"name": "ringbuffer", "files": [{"path": "../../Components/ringbuffer/ringbuffer.c"}], "folders": []}]}, {"name": "APP", "files": [{"path": "../../APP/mydefine.h"}, {"path": "../../APP/scheduler.c"}, {"path": "../../APP/usart_app.c"}, {"path": "../../APP/led_app.c"}, {"path": "../../APP/btn_app.c"}, {"path": "../../APP/adc_app.c"}, {"path": "../../APP/dac_app.c"}, {"path": "../../APP/waveform_analyzer_app.c"}], "folders": []}, {"name": "::CMSIS", "files": [], "folders": []}, {"name": "Middlewares", "files": [], "folders": [{"name": "Library", "files": [], "folders": [{"name": "DSP Library", "files": [], "folders": [{"name": "DSP Library", "files": [{"path": "../../Middlewares/ST/ARM/DSP/Lib/arm_cortexM4l_math.lib"}], "folders": []}]}]}]}]}, "outDir": "build", "deviceName": "GD32F470VE", "packDir": ".pack/GigaDevice/GD32F4xx_DFP.3.0.3", "miscInfo": {"uid": "c39e71af1ff2bf81cbf026b0bc233872"}, "targets": {"GD32_Xifeng": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x30000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x10000000", "size": "0x10000"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x80000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "OpenOCD", "uploadConfig": {"bin": "", "target": "stm32f4x", "interface": "cmsis-dap", "baseAddr": "0x08000000"}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": [".", "../../Core/Inc", "../../Drivers/STM32F4xx_HAL_Driver/Inc", "../../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "../../Drivers/CMSIS/Device/ST/STM32F4xx/Include", "../../Drivers/CMSIS/Include", "../../Components/ebtn", "../../APP", "../../Components/ringbuffer", "../../Middlewares/ST/ARM/DSP/Inc", ".cmsis/include", "../RTE/_GD32_Xifeng"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32F429xx", "ARM_MATH_CM4", "GD32F470"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "", "rw-base": ""}}}}}, "version": "3.5"}